1. 用主办方提供的语音数据集，进行人声检测和说话人识别算法的实现；
2. 人声检测算法用于识别是否有人声活动；
3. 说话人识别算法用于识别当前说话人的身份。主办方将提供指定4个人的语音数据及对应人员ID（“小芯”，“小原”，“小思”，“小来”）。算法需要能够识别出语音数据中的人声是否属于4个人中的某个人：如果“是”，则给出对应人员ID；如果“否”，则需判定为“其他说话人”；
数据集说明
主办方为算法开发准备了音频数据集 VeriHealthi_Speaker_Recognition_Dataset，该语音数据
集包含主办方自采人声数据以及部分开源数据，数据集使用需要注意以下几点：
 主办方自采人声数据包含手机采集、开发板采集、电脑采集以及平板采集的音频数据，供
参赛者自行选择使用。由于决赛测试使用的是开发板采集的实时语音数据，所以使用提供
的数据集开发算法时，需要考虑在开发板数据上的适用性，建议训练时同时使用不同设备
采集的数据，来提高算法性能。
 数据集不同类别数据的样本量不同，参赛者需对数据集进行处理，平衡各种类别的样本分
布，参赛者也可自行调整或扩充数据集来提高算法性能。
 提供的自采人声数据包括连续音频(存放在数据集中不同设备目录的 wav 文件夹中)与裁剪
好的 10s 音频(存放在数据集中不同设备目录的 wav_cut 文件夹中)，参赛者可以自行对连
续音频进行裁剪与筛选，或者使用主办方提供的已经裁剪好的 10s 音频(按顺序无重叠裁剪)。
 提供的开源数据包括 noise 与 others 两个文件夹，其中 noise 为噪声数据，即非人声数据，
others 为人声数据。
 算法开发阶段，建议参赛者按照单通道、8K 采样率配置读取主办方提供的音频数据。
 主办方指定需要识别出的说话人为 XiaoXin、XiaoYuan、XiaoSi 以及 XiaoLai，其他人声需
要识别为其他说话人，而音乐、噪音或者猫狗叫声这类非人声需要识别为无人声。
2. 文件格式说明
路径为./noise 的文件夹中存储非人声开源数据，文件格式为.wav，采样率为 8K，单通道。其中
包含各种类型的非人声数据，分别放在 cat、dog、environment_sound、music、nature_sound、n
oise 子文件夹下，分别为猫叫数据、狗叫数据、环境音数据、音乐、自然音数据、随机噪声数据。
路径为./others 的文件夹中存储人声开源数据，文件格式为.wav，采样率为 8K，单通道。该文
件夹中包含若干不同说话人的音频。
路径为./phone 的文件夹中存储不同手机采集的数据，采样率、通道数不定，可直接从.wav 文件
中读取出采样率、通道数等相关配置。./phone/xxx/wav（xxx 为人员 ID）文件夹中为手机采集的未
裁剪原数据，文件格式为.wav。./phone/xxx/wav_cut 文件夹中为裁剪好的手机音频数据，按顺序无
重叠裁剪，每个音频时长为 10s，文件格式为.wav，裁剪好的数据采样率为 8K。
路径为./board 的文件夹中存储开发板采集的单通道数据，./board/xxx/bin 文件夹中为开发板采
集的纯数据文件，格式为.bin，小端存储，文件中单个样本点为 int16 类型，采样率为 8K，为连续未
裁剪的音频数据。./board/xxx/wav 中的.wav 文件是由.bin 文件转化而来的未裁剪音频数据，采样率
为 8K。./board/xxx/wav_cut 文件夹中为裁剪好的开发板音频数据，按顺序无重叠裁剪，每个音频时
长为 10s，文件格式为.wav，采样率为 8K。
路径为./pc 的文件夹中存储电脑采集的数据，采样率、通道数不定，可直接从.wav 文件中读取
出采样率、通道数等相关配置。./pc/xxx/wav 文件夹中为电脑采集的未裁剪原数据，文件格式为.wa
v。./pc/xxx/wav_cut 文件夹中为裁剪好的电脑音频数据，按顺序无重叠裁剪，每个音频时长为 10s，
文件格式为.wav，裁剪好的数据采样率为 8K。
路径为./ipad 的文件夹中存储平板采集的数据，采样率、通道数不定，可直接从.wav 文件中读
取出采样率、通道数等相关配置。./ipad/xxx/wav 文件夹中为平板采集的未裁剪原数据，文件格式为.
wav。./ipad/xxx/wav_cut 文件夹中为裁剪好的平板音频数据，按顺序无重叠裁剪，每个音频时长为 1
0s，文件格式为.wav，采样率为 8K。
在上述的./phone 、./board 、./pc 以及./ipad 这 4 个自采数据文件夹中，./phone 与./board
Page 6 of 6
VERISILICON CONFIDENTIAL-NOT FOR REDISTRIBUTION
VeriHealthi Speaker Recognition Dataset
中包含的 ID1、ID2、ID3、ID4、ID5、ID6、ID7、ID8、ID9、ID10、ID11、XiaoXin、XiaoYuan、X
iaoSi 以及 XiaoLai 子文件夹分别对应 15 个不同说话人的音频。./pc 与./ipad 中包含的 ID1、ID2、I
D3、ID4、XiaoXin、XiaoYuan、XiaoSi 以及 XiaoLai 子文件夹分别对应 8 个不同说话人的音频。此
外，在上述 4 个自采数据文件夹中，名字相同的子文件夹对应的说话人也相同。例如，./phone/ID1
和./pc/ID1 中的说话人为同一个人，区别在于采集音频的设备不同，以及两个 ID1 文件夹中的文件并
非一一对应。
XiaoXin、XiaoYuan、XiaoSi 和 XiaoLai 为主办方指定的需要识别出的说话人，ID1~ID11、./ot
hers 文件夹以及数据集之外的说话人均需要识别为其他说话人，而对于 noise 数据集以及数据集之
外的非人声需要识别为无人声


目前的模型选型：
设计思路
这个模型采用**多任务学习（Multi-Task Learning）**的架构，其核心思想是：

共享主干网络 (Shared Backbone): VAD 和 SR 任务都依赖于从音频中提取高质量的声学特征。因此，它们可以共享一个强大的特征提取主干网络。这不仅极大地减少了总参数量，还能让两个任务相互促进，学习到更鲁棒的特征表示。我们将复用之前 TinySV 的主干。

专用任务头 (Specific Task Heads): 在共享主干网络之后，模型会分出两个分支，每个分支连接一个专门为特定任务设计的“头”：

VAD 头: 一个轻量级的分类头，对主干网络输出的每一帧特征进行判断，输出该帧是语音的概率。这是一个**序列到序列（Seq2Seq）**的任务。

SR 头: 这个头负责生成最终的说话人嵌入向量（声纹）。关键在于，它只应该聚合包含语音的帧的特征。因此，我们会利用 VAD 头的输出作为注意力权重，来指导 SR 头的池化过程，让模型只“关注”语音部分，忽略静音和噪声，从而得到更纯净、更准确的声纹。

这种设计使得一次前向传播就能同时得到两个结果：

音频中哪些部分是人声 (VAD)。

整段音频的人声是谁的 (SR)。

集成模型的 PyTorch 实现
以下是完整的、可直接运行的代码。它包含了所有必要的模块。

Python

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math

# -----------------------------------------------------------------------------
# 模块 1: SincConv Layer (与之前相同)
# -----------------------------------------------------------------------------
class SincConv_fast(nn.Module):
    @staticmethod
    def to_mel(hz):
        return 2595 * np.log10(1 + hz / 700)
    @staticmethod
    def to_hz(mel):
        return 700 * (10**(mel / 2595) - 1)
    def __init__(self, in_channels, out_channels, kernel_size, sample_rate=8000, stride=1, min_low_hz=50, min_band_hz=50):
        super().__init__()
        self.in_channels, self.out_channels, self.kernel_size, self.sample_rate = in_channels, out_channels, kernel_size, sample_rate
        self.stride, self.min_low_hz, self.min_band_hz = stride, min_low_hz, min_band_hz
        if self.kernel_size % 2 == 0: self.kernel_size += 1
        self.n_ = torch.arange(-self.kernel_size//2, self.kernel_size//2 + 1).view(1, -1)
        low_hz, high_hz = self.min_low_hz, self.sample_rate / 2 - (self.min_low_hz + self.min_band_hz)
        mel = torch.linspace(self.to_mel(low_hz), self.to_mel(high_hz), self.out_channels + 1)
        hz = self.to_hz(mel)
        self.low_hz_ = nn.Parameter(hz[:-1].view(-1, 1))
        self.band_hz_ = nn.Parameter((hz[1:] - hz[:-1]).view(-1, 1))
        self.window_ = torch.hamming_window(self.kernel_size)

    def forward(self, x):
        self.n_ = self.n_.to(x.device)
        self.window_ = self.window_.to(x.device)
        f_low = torch.abs(self.low_hz_) + self.min_low_hz
        f_high = torch.clamp(f_low + self.band_hz_, self.min_low_hz, self.sample_rate / 2)
        band = (f_high - f_low)
        f_c = (f_low + f_high) / 2
        n_pi = self.n_ * (2 * math.pi)
        low_pass1 = 2 * f_c * torch.sinc(n_pi * f_c / self.sample_rate)
        low_pass2 = 2 * (f_c + band/2) * torch.sinc(n_pi * (f_c + band/2) / self.sample_rate)
        band_pass = (low_pass2 - low_pass1)
        band_pass = band_pass / band_pass.max(dim=1, keepdim=True)[0]
        filters = (band_pass * self.window_).view(self.out_channels, 1, self.kernel_size)
        return F.conv1d(x, filters, stride=self.stride, padding=self.kernel_size//2, groups=self.out_channels)


# -----------------------------------------------------------------------------
# 模块 2: 残差块与注意力模块 (与之前相同)
# -----------------------------------------------------------------------------
class SEBlock(nn.Module):
    def __init__(self, channels, reduction=8):
        super().__init__()
        self.pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(nn.Linear(channels, channels // reduction, bias=False), nn.ReLU(inplace=True), nn.Linear(channels // reduction, channels, bias=False), nn.Sigmoid())
    def forward(self, x):
        b, c, _ = x.size(); y = self.pool(x).view(b, c); y = self.fc(y).view(b, c, 1); return x * y.expand_as(x)

class ResBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super().__init__()
        self.conv1 = nn.Conv1d(in_channels, out_channels, 3, stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm1d(out_channels); self.relu = nn.ReLU(inplace=True)
        self.conv2 = nn.Conv1d(out_channels, out_channels, 3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm1d(out_channels); self.se = SEBlock(out_channels)
        self.shortcut = nn.Sequential(nn.Conv1d(in_channels, out_channels, 1, stride, bias=False), nn.BatchNorm1d(out_channels)) if stride != 1 or in_channels != out_channels else nn.Identity()
    def forward(self, x):
        residual = self.shortcut(x); out = self.relu(self.bn1(self.conv1(x))); out = self.bn2(self.conv2(out)); out = self.se(out); out += residual; return self.relu(out)

class AttentiveStatPooling(nn.Module):
    def __init__(self, in_dim, bottleneck_dim=128):
        super().__init__(); self.tdnn = nn.Sequential(nn.Conv1d(in_dim, bottleneck_dim, 1), nn.ReLU(), nn.BatchNorm1d(bottleneck_dim), nn.Conv1d(bottleneck_dim, in_dim, 1))
    def forward(self, x, mask=None):
        alpha = torch.softmax(self.tdnn(x), dim=2)
        if mask is not None: alpha = alpha * mask
        mean = torch.sum(alpha * x, dim=2)
        var = torch.sum(alpha * (x**2), dim=2) - mean**2
        std = torch.sqrt(var.clamp(min=1e-5))
        return torch.cat([mean, std], dim=1)

# -----------------------------------------------------------------------------
# 模块 3: 共享主干网络
# -----------------------------------------------------------------------------
class SharedBackbone(nn.Module):
    def __init__(self, in_channels=1, channels=32, sample_rate=8000):
        super().__init__()
        self.frontend = SincConv_fast(in_channels, channels, kernel_size=129, sample_rate=sample_rate)
        self.bn_frontend = nn.BatchNorm1d(channels)
        self.layers = nn.Sequential(
            ResBlock(channels, channels, stride=2),
            ResBlock(channels, channels * 2, stride=2),
            ResBlock(channels * 2, channels * 4, stride=2),
            ResBlock(channels * 4, channels * 4)
        )
        self.output_channels = channels * 4

    def forward(self, x):
        x = self.frontend(x)
        x = F.leaky_relu(self.bn_frontend(x))
        x = self.layers(x)
        return x

# -----------------------------------------------------------------------------
# 模块 4: VAD + SR 集成模型
# -----------------------------------------------------------------------------
class VAD_SR_Model(nn.Module):
    def __init__(self, in_channels=1, channels=32, embedding_size=192, sample_rate=8000):
        super().__init__()
        
        # 共享主干网络
        self.backbone = SharedBackbone(in_channels, channels, sample_rate)
        backbone_out_channels = self.backbone.output_channels

        # 1. VAD 任务头 (逐帧分类)
        # 使用一个简单的1D卷积作为分类器
        self.vad_head = nn.Conv1d(in_channels=backbone_out_channels, out_channels=1, kernel_size=1)

        # 2. SR 任务头 (生成声纹)
        self.sr_pooling = AttentiveStatPooling(in_dim=backbone_out_channels)
        self.sr_embedding = nn.Sequential(
            nn.Linear(backbone_out_channels * 2, embedding_size),
            nn.BatchNorm1d(embedding_size)
        )

    def forward(self, x):
        """
        一次前向传播，同时输出声纹和VAD结果
        x: 原始波形, shape: [batch_size, num_samples]
        """
        if x.ndim == 2:
            x = x.unsqueeze(1) # 保证输入是 [B, 1, N]
        
        # 1. 通过共享主干网络提取特征
        # frame_features shape: [B, C, T_frames]
        frame_features = self.backbone(x)

        # 2. VAD 分支: 计算每一帧的语音活动概率
        # vad_logits shape: [B, 1, T_frames]
        vad_logits = self.vad_head(frame_features)

        # 3. SR 分支: 利用VAD结果指导声纹提取
        # 使用VAD的概率作为注意力掩码，让模型只关注语音帧
        with torch.no_grad(): # VAD分数不反向传播到SR分支，避免干扰
            vad_scores = torch.sigmoid(vad_logits)

        # 将VAD分数应用到特征上
        # masked_features = frame_features * vad_scores # 硬性相乘
        
        # 将语音活动分数作为注意力掩码传入池化层
        pooled_features = self.sr_pooling(frame_features, mask=vad_scores)
        
        # 生成最终的声纹嵌入
        speaker_embedding = self.sr_embedding(pooled_features)

        # 返回两个任务的结果
        # speaker_embedding: [B, embedding_size]
        # vad_logits: [B, 1, T_frames] -> squeeze -> [B, T_frames]
        return speaker_embedding, vad_logits.squeeze(1)


# -----------------------------------------------------------------------------
# 使用示例
# -----------------------------------------------------------------------------
if __name__ == '__main__':
    # 模型参数
    sr = 8000
    duration = 4  # 4秒音频
    num_samples = sr * duration
    batch_size = 10
    
    # 实例化集成模型
    model = VAD_SR_Model(
        channels=32,
        embedding_size=192
    )
    
    # 创建一个虚拟的输入张量
    dummy_input = torch.randn(batch_size, num_samples)
    print(f"输入张量形状: {dummy_input.shape}")

    # 将模型设置为评估模式
    model.eval()
    
    # 前向传播
    with torch.no_grad():
        speaker_embedding, vad_logits = model(dummy_input)

    print(f"\n--- 输出结果 ---")
    print(f"说话人识别 (SR) 输出形状: {speaker_embedding.shape}")
    print(f"声音活动检测 (VAD) 输出形状: {vad_logits.shape}")
    
    # 打印模型参数量
    num_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n模型总参数量: {num_params / 1e6:.2f} M")
如何训练与推理
如何训练
这是一个多任务模型，因此总损失是两个任务损失的加权和：
L_total = L_sr + alpha * L_vad

L_sr (说话人识别损失): 对输出的 speaker_embedding 使用度量学习损失，如 AAMSoftmax (ArcFace)、Triplet Loss 或 Prototypical Loss。你需要有说话人身份的标签。

L_vad (VAD损失): 对输出的 vad_logits 使用二元交叉熵损失 torch.nn.BCEWithLogitsLoss。你需要有每一帧的语音/非语音标签。

alpha: 一个超参数，用于平衡两个任务的重要性。可以从 0.1 或 0.5 开始尝试。

如何推理
一次推理即可获得所有信息：

将你的音频片段输入模型： speaker_embedding, vad_logits = model(audio_waveform)。

获取声纹: speaker_embedding 就是可以直接用于对比的声纹向量。

获取人声活动区间:

计算 VAD 概率：vad_probs = torch.sigmoid(vad_logits)。

将概率大于某个阈值（如 0.5）的帧识别为语音帧。

由于模型中的卷积和下采样，vad_logits 的时间维度 T_frames 会小于原始音频的采样点数 N。你需要计算下采样率，将帧级别的 VAD 结果映射回原始音频的时间戳，从而得到精确的人声开始和结束时间。

这个集成模型架构功能强大且高效，是工业界处理此类复合需求的常用方案。



给我完整的训练代码
